'use client';

import { But<PERSON> } from '@/components/ui/button';
import { ProgressIndicator } from './progress-indicator';
import Image from 'next/image';

interface WorkTypeStepProps {
  onNext: (workType: string) => void;
}

export function WorkTypeStep({ onNext }: WorkTypeStepProps) {
  const handleGetStarted = () => {
    onNext('Getting Started');
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-8 bg-background">
      {/* Progress Indicator */}
      <div className="absolute top-8 left-8">
        <ProgressIndicator currentStep={2} totalSteps={2} />
      </div>

      {/* Main Content */}
      <div className="w-full max-w-5xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl md:text-4xl font-bold">
            You're all set! Here's what you can do next:
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Connect your favorite tools and start automating your workflows with Atlas
          </p>
        </div>

        {/* Dashboard Screenshot */}
        <div className="max-w-4xl mx-auto">
          <div className="relative rounded-lg border-2 border-border overflow-hidden shadow-2xl">
            <Image
              src="/hero-ui.png"
              alt="Atlas Agents Dashboard"
              width={1200}
              height={800}
              className="w-full h-auto"
              priority
            />
          </div>
        </div>

        {/* Instructions */}
        <div className="max-w-2xl mx-auto space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="text-center p-4 rounded-lg bg-muted/30">
              <div className="text-2xl mb-2">🔗</div>
              <h3 className="font-semibold mb-1">Connect Tools</h3>
              <p className="text-sm text-muted-foreground">
                Browse and connect MCP servers to add powerful integrations
              </p>
            </div>
            <div className="text-center p-4 rounded-lg bg-muted/30">
              <div className="text-2xl mb-2">💬</div>
              <h3 className="font-semibold mb-1">Start Chatting</h3>
              <p className="text-sm text-muted-foreground">
                Ask Atlas to help with tasks using your connected tools
              </p>
            </div>
          </div>

          <div className="text-center">
            <Button
              onClick={handleGetStarted}
              size="lg"
              className="px-8 py-3 text-lg"
            >
              Get Started
            </Button>
          </div>
        </div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-32 w-96 h-96 rounded-full bg-primary/5 blur-3xl" />
        <div className="absolute -bottom-40 -left-32 w-96 h-96 rounded-full bg-primary/5 blur-3xl" />
      </div>
    </div>
  );
}
